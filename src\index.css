body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubunt<PERSON>', 'Can<PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

* {
  box-sizing: border-box;
}
   /* width: 25vw !important;
    height: 25vh !important;
    top: 56px;
    left: 0; */
    /* max-width: none !important;
    max-height: none !important; */

/* @media (max-width: 480px) {  
  .klaro .cm-modal {
 
    left: initial !important;
    margin: 0 !important;
    border-radius: 0 !important;
    max-width: calc(100vw - 32px) !important;
    max-height: 80vh !important;  
    overflow-x: auto;  
    overflow-y: auto;
  }
} */
